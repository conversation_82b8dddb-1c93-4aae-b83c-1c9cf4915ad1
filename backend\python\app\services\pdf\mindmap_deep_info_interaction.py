# backend/python/app/services/pdf/mindmap_deep_info_interaction.py
import requests
import json
from app.config import Config
from flask import current_app

def get_deep_info_from_node(node_name, node_context="", original_pdf_text="", language="English"):
    """
    Sends node information to Node.js service and gets detailed deep information.
    
    Args:
        node_name (str): The name of the mindmap node to get deep info for
        node_context (str): Additional context about the node
        original_pdf_text (str): Original PDF text for context
        language (str): Language for the response
        
    Returns:
        dict: Deep information data from Node.js service
        
    Raises:
        ValueError: If Node.js service URL not configured or service returns error
        ConnectionError: If unable to connect to Node.js service
        TimeoutError: If request times out
    """
    if not Config.NODE_MINDMAP_SERVICE_URL:
        current_app.logger.error("Node.js mind map service URL not configured.")
        raise ValueError("Node.js service URL not configured.")

    # Prepare payload for deep information request
    payload = {
        "nodeName": node_name,
        "nodeContext": node_context,
        "originalPdfText": original_pdf_text,
        "language": language
    }
    headers = {'Content-Type': 'application/json'}
    
    # Construct the deep info endpoint URL
    deep_info_url = f"{Config.NODE_MINDMAP_SERVICE_URL}/generate-deep-info"
    
    current_app.logger.info(f"Sending deep info request to Node.js service: {deep_info_url}")
    current_app.logger.debug(f"Deep info payload: node='{node_name}', language='{language}', context_length={len(node_context)}")

    try:
        # Make request to Node.js service with timeout
        response = requests.post(
            deep_info_url, 
            json=payload, 
            headers=headers, 
            timeout=60  # 60 second timeout for deep info generation
        )
        
        current_app.logger.info(f"Node.js deep info service responded with status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                deep_info_data = response.json()
                current_app.logger.info(f"Successfully received deep info data for node: {node_name}")
                return deep_info_data
            except json.JSONDecodeError as e:
                current_app.logger.error(f"Failed to decode JSON response from Node.js deep info service: {e}")
                current_app.logger.error(f"Raw response: {response.text[:500]}...")
                raise ValueError(f"Invalid JSON response from Node.js service: {e}")
        else:
            # Handle error responses from Node.js service
            try:
                error_data = response.json()
                error_message = error_data.get('error', 'Unknown error from Node.js service')
                error_details = error_data.get('details', '')
            except json.JSONDecodeError:
                error_message = f"HTTP {response.status_code} error from Node.js service"
                error_details = response.text[:200] if response.text else "No error details"
            
            current_app.logger.error(f"Node.js deep info service error: {error_message} - {error_details}")
            raise ValueError(f"Node.js service error: {response.status_code} - {error_message}")
            
    except requests.exceptions.ConnectionError as e:
        current_app.logger.error(f"Connection error to Node.js deep info service: {e}")
        raise ConnectionError(f"Unable to connect to deep information generation service: {e}")
    except requests.exceptions.Timeout as e:
        current_app.logger.error(f"Timeout error from Node.js deep info service: {e}")
        raise TimeoutError(f"Deep information generation service timed out: {e}")
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Request error to Node.js deep info service: {e}")
        raise ValueError(f"Request error to deep information service: {e}")
    except Exception as e:
        current_app.logger.error(f"Unexpected error in deep info interaction: {e}")
        raise ValueError(f"Unexpected error during deep information generation: {e}")
