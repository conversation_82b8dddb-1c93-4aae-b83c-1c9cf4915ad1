// node_gemini_summarizer/routes/Tools/pdf/mindmapInternalRoutes.js
import express from 'express';
import { generateMindmapStructure } from '../../../controllers/tools/Pdf/mindmapInternalController.js';
import { generateDeepInformation } from '../../../controllers/tools/Pdf/mindmapDeepInfoController.js';

const router = express.Router();

// This endpoint will be called by the Python service
router.post('/generate-from-summary', generateMindmapStructure);

// Deep information endpoint for detailed node information
router.post('/generate-deep-info', generateDeepInformation);

export default router;