// backend/javascript/controllers/tools/Pdf/mindmapDeepInfoController.js
import crypto from 'crypto';
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from "@google/generative-ai";

// --- Gemini Configuration ---
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GEMINI_MODEL_NAME = process.env.GEMINI_MINDMAP_MODEL_NAME || "gemini-1.5-flash-latest";

let genAIInstance;
let modelInstance;

function initializeGemini() {
    if (!GEMINI_API_KEY) {
        console.error("[Mindmap Deep Info Controller FATAL ERROR]: GEMINI_API_KEY is not defined. Deep information generation will not work.");
        return null;
    }
    try {
        genAIInstance = new GoogleGenerativeAI(GEMINI_API_KEY);
        modelInstance = genAIInstance.getGenerativeModel({ model: GEMINI_MODEL_NAME });
        console.log(`[Mindmap Deep Info Controller] Initialized Gemini with model: ${GEMINI_MODEL_NAME}`);
        return modelInstance;
    } catch (error) {
        console.error("[Mindmap Deep Info Controller] Failed to initialize Gemini Service:", error);
        modelInstance = null;
        return null;
    }
}

// Initialize on load
if (!modelInstance) {
    modelInstance = initializeGemini();
}

function getGeminiModel() {
    if (!modelInstance) {
        modelInstance = initializeGemini();
    }
    return modelInstance;
}

// Cache for deep information responses
const deepInfoCache = new Map();

function parseGeminiJsonResponse(geminiResponseText, nodeName) {
    try {
        const jsonData = JSON.parse(geminiResponseText);
        return jsonData;
    } catch (parseError) {
        console.error("[Mindmap Deep Info Controller] Failed to parse JSON response from Gemini:", parseError);
        console.error("[Mindmap Deep Info Controller] Raw Gemini response causing parse error:", geminiResponseText);
        return {
            nodeName: nodeName,
            error: "Failed to parse deep information from AI. The response was not valid JSON.",
            details: parseError.message,
            rawResponse: process.env.NODE_ENV !== 'production' ? geminiResponseText.substring(0, 500) + "..." : undefined
        };
    }
}

export const generateDeepInformation = async (req, res) => {
    const { 
        nodeName,
        nodeContext = "",
        originalPdfText = "",
        language = 'English'
    } = req.body;
    const workerPid = process.pid;

    if (!nodeName) {
        console.warn(`[Deep Info Worker ${workerPid}] Received request without node name.`);
        return res.status(400).json({ error: 'Node name is required for deep information generation.' });
    }

    const model = getGeminiModel();
    if (!model) {
        console.error(`[Deep Info Worker ${workerPid}] Gemini model not initialized. Cannot generate deep information.`);
        return res.status(500).json({ error: 'AI Model for deep information generation is not available.' });
    }

    // Create cache key based on node name, context, and language
    const textHash = crypto.createHash('md5').update(`${nodeName}-${nodeContext}-${language}`).digest('hex');
    const cacheKey = `deepinfo-${textHash}`;

    if (deepInfoCache.has(cacheKey) && process.env.NODE_ENV !== 'development') {
        console.log(`[Deep Info Worker ${workerPid}] Returning cached deep information for: ${nodeName}`);
        return res.status(200).json(deepInfoCache.get(cacheKey));
    }

    console.log(`[Deep Info Worker ${workerPid}] Generating deep information for node: "${nodeName}" in ${language}...`);

    const prompt = `
        You are an AI assistant specialized in providing comprehensive, detailed information about specific topics.
        A user is exploring a mind map and has clicked on a node titled: "${nodeName}"
        
        ${nodeContext ? `Additional context about this node: ${nodeContext}` : ''}
        ${originalPdfText ? `This node is part of a mind map created from the following document content: ${originalPdfText.substring(0, 10000)}` : ''}
        
        Your task is to provide extremely detailed, comprehensive information about this specific topic.
        
        *** CRITICAL INSTRUCTION: The entire response, including all fields in the JSON, MUST be in the following language: ${language}. ***
        
        The output MUST be a single, valid JSON object with the following structure:
        {
            "nodeName": "${nodeName}",
            "title": "Comprehensive title for this topic (in ${language})",
            "overview": "Detailed 3-4 sentence overview explaining what this topic is and why it's important (in ${language})",
            "keyConceptsAndTitles": [
                {
                    "title": "Main Concept 1 Title (in ${language})",
                    "description": "Detailed explanation of this concept (in ${language})"
                },
                {
                    "title": "Main Concept 2 Title (in ${language})",
                    "description": "Detailed explanation of this concept (in ${language})"
                },
                {
                    "title": "Main Concept 3 Title (in ${language})",
                    "description": "Detailed explanation of this concept (in ${language})"
                }
            ],
            "detailedDescription": "Comprehensive explanation with examples, applications, and real-world relevance (4-6 sentences in ${language})",
            "practicalApplications": [
                "Practical application or example 1 (in ${language})",
                "Practical application or example 2 (in ${language})",
                "Practical application or example 3 (in ${language})"
            ],
            "relatedTopics": [
                {
                    "topic": "Related Topic 1 (in ${language})",
                    "relationship": "How this relates to the main topic (in ${language})"
                },
                {
                    "topic": "Related Topic 2 (in ${language})",
                    "relationship": "How this relates to the main topic (in ${language})"
                }
            ],
            "keyTakeaways": [
                "Important takeaway 1 (in ${language})",
                "Important takeaway 2 (in ${language})",
                "Important takeaway 3 (in ${language})"
            ]
        }
        
        IMPORTANT GUIDELINES:
        1. Provide rich, informative content that goes beyond basic definitions
        2. Include practical examples and real-world applications
        3. Make the information educational and actionable
        4. Ensure all content is accurate and well-researched
        5. Use clear, professional language appropriate for the topic
        6. Connect the topic to broader themes and related concepts
        7. Provide insights that help users understand the topic's significance
        
        Generate ONLY the valid JSON structure with comprehensive deep information about "${nodeName}", strictly in the ${language} language.
    `;

    try {
        const generationConfig = {
            temperature: 0.7,
            topK: 1,
            topP: 1,
            maxOutputTokens: 4096,
            responseMimeType: "application/json",
        };

        const safetySettings = [
            { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
            { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
            { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
            { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
        ];

        console.log(`[Deep Info Worker ${workerPid}] Sending prompt to Gemini for node: ${nodeName} in language: ${language}`);

        const result = await model.generateContent({
            contents: [{ role: "user", parts: [{text: prompt}] }],
            generationConfig,
            safetySettings,
        });

        if (!result.response) {
            console.error(`[Deep Info Worker ${workerPid}] No response received from Gemini for node: ${nodeName}`);
            return res.status(500).json({ error: 'No response received from AI service.' });
        }

        const responseText = result.response.text();
        if (!responseText) {
            console.error(`[Deep Info Worker ${workerPid}] Empty response text from Gemini for node: ${nodeName}`);
            return res.status(500).json({ error: 'Empty response received from AI service.' });
        }

        console.log(`[Deep Info Worker ${workerPid}] Received response from Gemini for node: ${nodeName}. Length: ${responseText.length}`);

        const deepInfoData = parseGeminiJsonResponse(responseText, nodeName);

        if (deepInfoData.error) {
            console.warn(`[Deep Info Worker ${workerPid}] Deep info data from Gemini contained an error: ${deepInfoData.error}`);
            return res.status(deepInfoData.rawResponse ? 500 : 200).json(deepInfoData);
        }

        // Cache the response
        deepInfoCache.set(cacheKey, deepInfoData);
        // Evict oldest entry if cache grows too large
        if (deepInfoCache.size > 200) {
            const firstKey = deepInfoCache.keys().next().value;
            deepInfoCache.delete(firstKey);
        }

        console.log(`[Deep Info Worker ${workerPid}] Successfully generated deep information for node: ${nodeName}`);
        return res.status(200).json(deepInfoData);

    } catch (error) {
        console.error(`[Deep Info Worker ${workerPid}] Error generating deep information for node ${nodeName}:`, error);
        if (error.message && (error.message.includes("API key not valid") || error.message.includes("PERMISSION_DENIED"))) {
             return res.status(401).json({ error: 'AI service authentication failed.', details: error.message });
        }
        if (error.message && error.message.includes("Rate limit exceeded")) {
             return res.status(429).json({ error: 'AI service rate limit exceeded.', details: error.message });
        }
        res.status(500).json({
            error: 'Failed to generate deep information using AI.',
            details: error.message
        });
    }
};
