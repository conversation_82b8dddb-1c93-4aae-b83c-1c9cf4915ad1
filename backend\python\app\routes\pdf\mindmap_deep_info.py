# backend/python/app/routes/pdf/mindmap_deep_info.py
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.user import User
from app.services.pdf.mindmap_deep_info_interaction import get_deep_info_from_node
import traceback

mindmap_deep_info_bp = Blueprint('mindmap_deep_info', __name__)

@mindmap_deep_info_bp.route('/generate-deep-info', methods=['POST'])
@jwt_required()
def generate_deep_information():
    """
    Generate detailed information for a specific mindmap node.
    Includes usage tracking and limits for free vs premium users.
    """
    try:
        current_user_id = get_jwt_identity()
        current_app.logger.info(f"Deep info request from user: {current_user_id}")
        
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400
            
        node_name = data.get('nodeName')
        node_context = data.get('nodeContext', '')
        original_pdf_text = data.get('originalPdfText', '')
        language = data.get('language', 'English')
        
        if not node_name:
            return jsonify({"error": "Node name is required"}), 400
            
        # Get user and check subscription
        user = User.objects(id=current_user_id).first()
        if not user:
            return jsonify({"error": "User not found"}), 404
            
        subscription = user.subscription
        if not subscription:
            return jsonify({"error": "No subscription found"}), 400
            
        # Check usage limits
        plan_name = subscription.planName
        current_usage = getattr(subscription, 'mindmapDeepInfoCount', 0)
        
        # Define limits based on plan
        if plan_name == 'Starter':
            usage_limit = 2  # Free users get 2 uses
        elif plan_name == 'Pro':
            usage_limit = 999999  # Premium users get unlimited
        else:
            usage_limit = 0  # Unknown plan, no access
            
        # Check if user has exceeded limit
        if current_usage >= usage_limit:
            return jsonify({
                "error": "Deep Information usage limit exceeded",
                "currentUsage": current_usage,
                "limit": usage_limit,
                "planName": plan_name,
                "message": f"You have used {current_usage}/{usage_limit} Deep Information requests. Upgrade to Pro for unlimited access."
            }), 429
            
        current_app.logger.info(f"Generating deep info for node: {node_name} (User: {current_user_id}, Usage: {current_usage}/{usage_limit})")
        
        # Generate deep information via Node.js service
        try:
            deep_info_data = get_deep_info_from_node(
                node_name=node_name,
                node_context=node_context,
                original_pdf_text=original_pdf_text,
                language=language
            )
            
            # Increment usage count only after successful generation
            if plan_name == 'Starter':
                # Only track usage for free users
                subscription.mindmapDeepInfoCount = current_usage + 1
                user.save()
                current_app.logger.info(f"Updated deep info usage count for user {current_user_id}: {current_usage + 1}/{usage_limit}")
            
            # Add usage information to response
            deep_info_data['usageInfo'] = {
                'currentUsage': current_usage + 1 if plan_name == 'Starter' else current_usage,
                'limit': usage_limit,
                'planName': plan_name,
                'remainingUses': max(0, usage_limit - (current_usage + 1)) if plan_name == 'Starter' else 'unlimited'
            }
            
            current_app.logger.info(f"Successfully generated deep info for node: {node_name}")
            return jsonify(deep_info_data), 200
            
        except ConnectionError as e:
            current_app.logger.error(f"Node.js Connection Error for deep info: {e}", exc_info=True)
            return jsonify({"error": "Could not connect to deep information generation service.", "details": str(e)}), 503
        except TimeoutError as e:
            current_app.logger.error(f"Node.js Timeout Error for deep info: {e}", exc_info=True)
            return jsonify({"error": "Deep information generation service timed out.", "details": str(e)}), 504
        except ValueError as e:
            current_app.logger.error(f"Error from Node.js service for deep info: {e}", exc_info=False)
            status_code = 502
            if "Node.js service error: 400" in str(e):
                status_code = 400
            return jsonify({"error": "Deep information generation service failed.", "details_from_node": str(e)}), status_code
        except Exception as e:
            current_app.logger.error(f"Unexpected error during deep info generation: {e}", exc_info=True)
            return jsonify({"error": "An unexpected error occurred during deep information generation.", "details": str(e)}), 500
            
    except Exception as e:
        current_app.logger.error(f"Error in generate_deep_information endpoint: {e}", exc_info=True)
        return jsonify({
            "error": "An error occurred while processing your request.",
            "details": str(e) if current_app.debug else "Internal server error"
        }), 500

@mindmap_deep_info_bp.route('/usage-info', methods=['GET'])
@jwt_required()
def get_deep_info_usage():
    """
    Get current usage information for the Deep Information feature.
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Get user and check subscription
        user = User.objects(id=current_user_id).first()
        if not user:
            return jsonify({"error": "User not found"}), 404
            
        subscription = user.subscription
        if not subscription:
            return jsonify({"error": "No subscription found"}), 400
            
        # Get usage information
        plan_name = subscription.planName
        current_usage = getattr(subscription, 'mindmapDeepInfoCount', 0)
        
        # Define limits based on plan
        if plan_name == 'Starter':
            usage_limit = 2
        elif plan_name == 'Pro':
            usage_limit = 999999
        else:
            usage_limit = 0
            
        return jsonify({
            'currentUsage': current_usage,
            'limit': usage_limit,
            'planName': plan_name,
            'remainingUses': max(0, usage_limit - current_usage) if plan_name == 'Starter' else 'unlimited',
            'hasAccess': current_usage < usage_limit
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error in get_deep_info_usage endpoint: {e}", exc_info=True)
        return jsonify({
            "error": "An error occurred while fetching usage information.",
            "details": str(e) if current_app.debug else "Internal server error"
        }), 500
